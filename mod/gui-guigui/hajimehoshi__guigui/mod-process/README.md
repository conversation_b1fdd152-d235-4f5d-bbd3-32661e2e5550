# mod-pc


https://github.com/F1bonacc1/process-compose

https://github.com/F1bonacc1/process-compose/blob/main/go.mod

https://github.com/F1bonacc1/process-compose/blob/main/src/main.go

The golang package overrides are:

https://github.com/f1bonacc1/netstat/

https://github.com/f1bonacc1/go-health/ 

## Copied from their repo

I just copied the main.go and the go.mod file from process-compose. 

I then just changed the package name to main.

## What we need ?


We need our own main.go to use this package so we have full control.

We do not want any of the TUI from process-compose.

We want a Datastar Web GUI to see and control what processes it is running. 

- I am guessing that the easiest way to control the processes is to use the REST API ? 

















