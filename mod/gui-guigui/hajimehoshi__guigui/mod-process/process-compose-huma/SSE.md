# Feature Request: Server-Sent Events (SSE) Support for Real-time Updates

## Summary

Add Server-Sent Events (SSE) endpoints to the process-compose REST API to enable real-time streaming of process status updates, logs, and system events without requiring constant polling.

## Motivation

Currently, process-compose provides excellent REST API endpoints for retrieving process information, but real-time monitoring requires constant polling of these endpoints. This approach has several limitations:

- **Inefficient**: Constant polling wastes bandwidth and server resources
- **Delayed Updates**: Polling intervals create delays in receiving updates
- **Complex Client Logic**: Clients must implement polling logic and handle rate limiting
- **Poor User Experience**: Web dashboards and monitoring tools feel sluggish

SSE would enable efficient real-time communication for building modern web interfaces and monitoring dashboards.

## Proposed Solution

Add the following SSE endpoints to complement the existing REST API:

### 1. Process Status Stream
```
GET /events/processes
Accept: text/event-stream
```

Stream real-time updates when process status changes (started, stopped, completed, failed, restarted).

**Example Event:**
```
event: process_status_changed
data: {"name": "example1", "status": "Running", "pid": 12345, "timestamp": "2024-06-12T18:15:30Z"}

event: process_status_changed  
data: {"name": "example2", "status": "Completed", "exit_code": 0, "timestamp": "2024-06-12T18:16:45Z"}
```

### 2. Process Logs Stream
```
GET /events/logs/{process_name}
Accept: text/event-stream
```

Stream real-time log output from a specific process.

**Example Event:**
```
event: log_line
data: {"process": "example1", "line": "Application started successfully", "timestamp": "2024-06-12T18:15:30Z"}
```

### 3. System Events Stream
```
GET /events/system
Accept: text/event-stream
```

Stream system-level events like project start/stop, configuration reloads, etc.

**Example Event:**
```
event: project_started
data: {"processes_count": 3, "timestamp": "2024-06-12T18:15:00Z"}

event: configuration_reloaded
data: {"config_files": ["process-compose.yaml"], "timestamp": "2024-06-12T18:20:00Z"}
```

### 4. Combined Events Stream
```
GET /events/all
Accept: text/event-stream
```

Stream all events in a single connection for comprehensive monitoring.

## Implementation Details

### Recommended Implementation: Huma v2 with SSE Package

The ideal implementation would use the **Huma v2 framework** with its built-in SSE support, which automatically generates OpenAPI documentation for SSE endpoints:

```go
import (
    "github.com/danielgtaylor/huma/v2"
    "github.com/danielgtaylor/huma/v2/sse"
)

// Define event structures
type ProcessStatusEvent struct {
    Name      string `json:"name"`
    Status    string `json:"status"`
    PID       int    `json:"pid,omitempty"`
    ExitCode  *int   `json:"exit_code,omitempty"`
    Timestamp string `json:"timestamp"`
}

type LogEvent struct {
    Process   string `json:"process"`
    Line      string `json:"line"`
    Timestamp string `json:"timestamp"`
}

// Register SSE endpoint with automatic OpenAPI generation
sse.Register(api, huma.Operation{
    OperationID: "subscribeToProcessEvents",
    Method:      http.MethodGet,
    Path:        "/events/processes",
    Summary:     "Subscribe to real-time process events",
    Tags:        []string{"SSE"},
}, map[string]any{
    "process_status_changed": ProcessStatusEvent{},
    "log_line":              LogEvent{},
    "system_event":          SystemEvent{},
}, func(ctx context.Context, input *struct{}, send sse.Sender) {
    // SSE handler implementation
    for {
        select {
        case <-ctx.Done():
            return
        case event := <-processEventChannel:
            send.Data(event)
        }
    }
})
```

**Benefits of Huma v2 SSE:**
- ✅ **Automatic OpenAPI Documentation** - SSE endpoints are fully documented
- ✅ **Type Safety** - Event structures are validated at compile time
- ✅ **Schema Generation** - JSON schemas generated for all event types
- ✅ **Built-in Error Handling** - Proper HTTP error responses
- ✅ **Standards Compliant** - Follows SSE specification exactly

### Event Types
- `process_status_changed` - Process state transitions
- `process_started` - Process startup
- `process_stopped` - Process shutdown
- `process_restarted` - Process restart
- `log_line` - New log output
- `project_started` - Project initialization
- `project_stopped` - Project shutdown
- `configuration_reloaded` - Config file changes
- `health_check_failed` - Health check failures
- `health_check_recovered` - Health check recovery

### Query Parameters
- `?filter=process1,process2` - Filter events for specific processes
- `?since=timestamp` - Start streaming from a specific time
- `?types=process_status_changed,log_line` - Filter by event types

### Connection Management
- Automatic reconnection support with `Last-Event-ID` header
- Configurable keep-alive intervals
- Graceful connection cleanup on client disconnect

### Configuration Options
Add new configuration options to control SSE behavior:

```yaml
options:
  sse:
    enabled: true
    max_connections: 100
    keep_alive_interval: 30s
    buffer_size: 1000
    log_streaming: true
```

## Use Cases

### 1. Web Dashboard Development
```javascript
const eventSource = new EventSource('/events/processes');
eventSource.onmessage = function(event) {
    const data = JSON.parse(event.data);
    updateProcessStatus(data.name, data.status);
};
```

### 2. Real-time Monitoring Tools
```bash
curl -H "Accept: text/event-stream" http://localhost:8080/events/all
```

### 3. CI/CD Integration
Real-time build status updates without polling delays.

### 4. Development Workflow
Live log tailing during development:
```bash
curl -H "Accept: text/event-stream" http://localhost:8080/events/logs/my-service
```

## Benefits

- **Reduced Server Load**: Eliminates constant polling
- **Real-time Updates**: Instant notification of changes
- **Better User Experience**: Responsive web interfaces
- **Standard Protocol**: SSE is widely supported by browsers and tools
- **Backward Compatible**: Existing REST API remains unchanged
- **Efficient**: Only sends data when changes occur

## Alternative Approaches Considered

1. **WebSockets**: More complex, bidirectional when only server-to-client is needed
2. **Long Polling**: Still requires client-side complexity and connection management
3. **gRPC Streaming**: Would require additional dependencies and complexity

SSE is the most appropriate choice for this use case as it's:
- Simple to implement and use
- Natively supported by browsers
- Automatically handles reconnection
- Perfect for server-to-client streaming

## Implementation Priority

**High Priority:**
- Basic process status change events
- Process log streaming

**Medium Priority:**  
- System events
- Event filtering and querying
- Configuration options

**Low Priority:**
- Advanced filtering
- Event replay functionality
- Metrics and analytics events

## Compatibility

This feature would be:
- ✅ **Backward Compatible**: No changes to existing REST API
- ✅ **Optional**: Can be disabled via configuration
- ✅ **Standards Compliant**: Uses standard SSE protocol
- ✅ **Cross-Platform**: Works on all platforms process-compose supports

## Go SSE Client Support

For Go applications (including TUI tools) that need to consume SSE events, several options are available:

### Recommended Go SSE Libraries

1. **github.com/r3labs/sse/v2** - Popular, well-maintained SSE client
2. **github.com/donovanhide/eventsource** - Simple, lightweight SSE client
3. **github.com/tmaxmax/go-sse** - Modern SSE client with good error handling

### Example Go TUI Integration

```go
package main

import (
    "encoding/json"
    "fmt"
    "log"

    "github.com/r3labs/sse/v2"
)

type ProcessEvent struct {
    Name      string `json:"name"`
    Status    string `json:"status"`
    PID       int    `json:"pid"`
    Timestamp string `json:"timestamp"`
}

func main() {
    client := sse.NewClient("http://localhost:8080/events/processes")

    // Subscribe to process status changes
    client.Subscribe("process_status_changed", func(msg *sse.Event) {
        var event ProcessEvent
        if err := json.Unmarshal(msg.Data, &event); err != nil {
            log.Printf("Error parsing event: %v", err)
            return
        }

        // Update TUI with new process status
        updateTUIProcessStatus(event.Name, event.Status, event.PID)
    })

    // Handle connection errors
    client.OnDisconnect(func(c *sse.Client) {
        log.Println("SSE connection lost, attempting reconnect...")
    })

    // Start listening (blocking)
    if err := client.SubscribeRaw(func(msg *sse.Event) {
        // Handle raw events if needed
    }); err != nil {
        log.Fatal("SSE connection failed:", err)
    }
}

func updateTUIProcessStatus(name, status string, pid int) {
    // TUI update logic here
    fmt.Printf("Process %s is now %s (PID: %d)\n", name, status, pid)
}
```

### TUI Framework Integration

For popular Go TUI frameworks:

**Bubble Tea (charm.sh):**
```go
type sseMsg ProcessEvent

func listenForSSE() tea.Cmd {
    return func() tea.Msg {
        // SSE client code here
        return sseMsg(event)
    }
}
```

**tview:**
```go
func (app *App) startSSEListener() {
    go func() {
        // SSE client in goroutine
        // Update tview components via app.QueueUpdateDraw()
    }()
}
```

## Related Issues

This feature would complement the existing REST API and TUI functionality, providing a modern foundation for building web-based monitoring and control interfaces for process-compose.
