# https://taskfile.dev

version: '3'

vars:
  GREETING: Hello, <PERSON>!

tasks:
  default:
    cmds:
      - echo "{{.GREETING}}"
    silent: true

  build:
    cmds:
      - cd ./process-compose-main && go build -o ./.bin/process-compose
      - cd ./process-compose-huma && go build -o ./.bin/process-compose-huma

      
  pack:up:
    cmds:
      # copy bins and config up
      - mkdir -p ./.bin
      - cp -r ./process-compose-main/.bin/. ./.bin/
      - cp -r ./process-compose-huma/.bin/. ./.bin/
      # copy config into .bin
      - cp -r ./process-compose.yaml ./.bin/

  pack:del:
      - rm -rf ./.bin/

  run:
    cmds:
      - cd ./.bin && ./process-compose-huma

