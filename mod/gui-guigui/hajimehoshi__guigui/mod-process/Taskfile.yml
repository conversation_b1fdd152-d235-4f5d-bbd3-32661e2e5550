# https://taskfile.dev

version: '3'

vars:
  GREETING: Hello, World!

tasks:
  default:
    cmds:
      - echo "{{.GREETING}}"
    silent: true

  build:
    cmds:
      - cd ./process-compose-main && task build
      - cd ./process-compose-huma && task build
      
  pack:up:
    cmds:
      # copy any sub .bins up 
      - cp -r ./process-compose-main/.bin/. ./.bin/
      - cp -r ./process-compose-huma/.bin/. ./.bin/

  run:
    cmds:
      - ./.bin/process-compose-huma

