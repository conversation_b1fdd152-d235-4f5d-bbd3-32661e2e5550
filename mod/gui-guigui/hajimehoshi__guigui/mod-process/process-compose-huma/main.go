package main

import (
	"context"
	"log"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/danielgtaylor/huma/v2"
	"github.com/danielgtaylor/huma/v2/adapters/humachi"
	"github.com/go-chi/chi/v5"
	"github.com/go-chi/chi/v5/middleware"

	"process-compose-huma/api"
	"process-compose-huma/wrapper"
)

func main() {
	log.Println("🚀 Starting Process Compose with Huma v2 API...")

	// Initialize process-compose wrapper with config path
	configPath := "process-compose.yaml" // Now uses simple config without circular dependency
	controller := wrapper.NewProcessComposeWrapper(configPath)

	// Start process-compose (this doesn't modify the main project)
	if err := controller.Start(); err != nil {
		log.Fatalf("Failed to start process-compose: %v", err)
	}

	// Ensure cleanup on exit
	defer func() {
		log.Println("🧹 Cleaning up...")
		controller.Stop()
	}()

	// Create Chi router
	router := chi.NewRouter()
	router.Use(middleware.Logger)
	router.Use(middleware.Recoverer)
	router.Use(middleware.Heartbeat("/ping"))

	// Create Huma API configuration
	config := huma.DefaultConfig("Process Compose API", "1.0.0")
	config.Info.Description = "Enhanced Process Compose API with real-time SSE support powered by Huma v2"
	config.Servers = []*huma.Server{
		{URL: "http://localhost:8888", Description: "Development server"},
	}

	// Create Huma API instance
	humaAPI := humachi.New(router, config)

	// Initialize API handlers
	apiHandlers := api.NewAPIHandlers(controller)
	sseHandlers := api.NewSSEHandlers(controller)

	// Register REST API routes
	apiHandlers.RegisterRoutes(humaAPI)

	// Register SSE routes
	sseHandlers.RegisterSSERoutes(humaAPI)

	// Create HTTP server
	server := &http.Server{
		Addr:    ":8888",
		Handler: router,
	}

	// Graceful shutdown handling
	go func() {
		sigChan := make(chan os.Signal, 1)
		signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)
		<-sigChan

		log.Println("🛑 Shutting down server...")
		ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
		defer cancel()

		if err := server.Shutdown(ctx); err != nil {
			log.Printf("Server shutdown error: %v", err)
		}
	}()

	// Start the server
	log.Printf("🌐 Enhanced Process Compose API starting on http://localhost:8888")
	log.Printf("📚 API Documentation: http://localhost:8888/docs")
	log.Printf("🔄 SSE Process Events: http://localhost:8888/events/processes")
	log.Printf("📝 SSE Log Events: http://localhost:8888/events/logs")
	log.Printf("🔧 SSE System Events: http://localhost:8888/events/system")
	log.Printf("🌐 SSE All Events: http://localhost:8888/events/all")
	log.Printf("💚 Health Check: http://localhost:8888/health")
	log.Printf("📊 Original Process Compose API: http://localhost:8080")

	if err := server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
		log.Fatalf("Server failed to start: %v", err)
	}

	log.Println("✅ Server stopped gracefully")
}
