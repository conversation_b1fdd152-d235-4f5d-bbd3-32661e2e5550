version: "0.5"
processes:
  # Core API and GUI processes
  huma-api:
    command: "./process-compose-huma"
    description: "Enhanced Process Compose API with SSE and OpenAPI docs"
    working_dir: "."
    availability:
      restart: "always"
    readiness_probe:
      http_get:
        host: "localhost"
        port: 8888
        path: "/health"
      initial_delay_seconds: 3
      period_seconds: 5
      timeout_seconds: 3
      success_threshold: 1
      failure_threshold: 3

  datastar-gui:
    command: "./process-compose-datastar"
    description: "Datastar GUI for Process Compose with real-time updates"
    working_dir: "."
    availability:
      restart: "always"
    depends_on:
      huma-api:
        condition: process_healthy
    readiness_probe:
      http_get:
        host: "localhost"
        port: 3000
        path: "/"
      initial_delay_seconds: 2
      period_seconds: 10
      timeout_seconds: 3
      success_threshold: 1
      failure_threshold: 3

  # Example processes for demonstration
  example1:
    command: "echo 'Hello from example1' && sleep 5"
    description: "Simple echo command that completes quickly"
    availability:
      restart: "no"
  example2:
    command: "while true; do echo 'Running example2...'; sleep 3; done"
    description: "Long-running process that outputs every 3 seconds"
    availability:
      restart: "always"
  example3:
    command: "echo 'Example3 starting' && ls -la && echo 'Example3 done'"
    description: "Another simple process that lists directory contents"
    availability:
      restart: "no"
    depends_on:
      example1:
        condition: process_completed_successfully

options:
  # Disable TUI as specified in the README
  disable_tui: true