# https://taskfile.dev

version: '3'

vars:
  GREETING: Hello, World!

tasks:
  default:
    cmds:
      - echo "{{.GREETING}}"
    silent: true

  ### dev ( with CLI_ARGS to the AI can use this.)

  dev:core:
    cmds:
      - cd ./process-compose && go run main.go {{.CLI_ARGS}}
    silent: true

  dev:api:
    cmds:
      - cd ./process-compose-huma && go run main.go {{.CLI_ARGS}}
    silent: true

  dev:gui:
    cmds:
      - cd ./process-compose-datastar && go run main.go {{.CLI_ARGS}}
    silent: true

  ### build

  build:
    cmds:
      - cd ./process-compose && go build -o ./.bin/process-compose
      - cd ./process-compose-huma && go build -o ./.bin/process-compose-huma
      - cd ./process-compose-datastar && go build -o ./.bin/process-compose-datastar
      
  # pack 

  pack:up:
    cmds:
      # copy bins and config up
      - mkdir -p ./.bin
      - cp -r ./process-compose/.bin/. ./.bin/
      - cp -r ./process-compose-huma/.bin/. ./.bin/
      - cp -r ./process-compose-datastar/.bin/. ./.bin/
      # copy config into .bin
      - cp -r ./process-compose.yaml ./.bin/
  pack:del:
      - rm -rf ./.bin/
  pack:list:
      - ls -al ./.bin/

  ### run

  run:core:
    cmds:
      - cd ./.bin && ./process-compose -p 8080 -t=false -f process-compose.yaml

  run:api:
    cmds:
      - cd ./.bin && ./process-compose-huma

  run:gui:
    cmds:
      - cd ./.bin && ./process-compose-datastar

  run:all:
    deps: [run:core, run:api, run:gui]

