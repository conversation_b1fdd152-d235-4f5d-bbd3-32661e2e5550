# huma openapi with sse

https://github.com/danielgtaylor/huma


Lets see if its good.

Good that its uses chi, like datastar

http://localhost:8888

## why huma ?

<PERSON><PERSON> can generate an OpenAPI (Swagger) specification directly from your Golang code, specifically from your Go structs and API handler definitions.

As demonstrated in the golang-openapi-sse Canvas, <PERSON><PERSON> analyzes the Go structs you register (like Message, HeartbeatEvent, UserConnectedEvent) and the huma.Operation definitions to automatically build the OpenAPI schema and paths. It then serves this generated specification at endpoints like /openapi.json and /openapi.yaml.

This approach allows you to define your API in Go, and <PERSON><PERSON> handles the conversion to an OpenAPI document, ensuring consistency between your code and its documentation.

## Gen off the Process-compose package ?





---

otherwise ogen ?