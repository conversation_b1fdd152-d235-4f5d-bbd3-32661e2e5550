package main

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"time"

	"github.com/r3labs/sse/v2"
)

// ProcessEvent represents a process status change event
type ProcessEvent struct {
	Name      string `json:"name"`
	Status    string `json:"status"`
	PID       int    `json:"pid"`
	ExitCode  *int   `json:"exit_code,omitempty"`
	Timestamp string `json:"timestamp"`
}

// LogEvent represents a log line event
type LogEvent struct {
	Process   string `json:"process"`
	Line      string `json:"line"`
	Timestamp string `json:"timestamp"`
}

// SystemEvent represents a system-level event
type SystemEvent struct {
	Type      string `json:"type"`
	Message   string `json:"message"`
	Timestamp string `json:"timestamp"`
}

// ProcessComposeSSEClient wraps SSE functionality for process-compose
type ProcessComposeSSEClient struct {
	baseURL string
	client  *sse.Client
	
	// Event handlers
	onProcessStatusChanged func(ProcessEvent)
	onLogLine             func(LogEvent)
	onSystemEvent         func(SystemEvent)
	onError               func(error)
	onDisconnect          func()
}

// NewProcessComposeSSEClient creates a new SSE client for process-compose
func NewProcessComposeSSEClient(baseURL string) *ProcessComposeSSEClient {
	return &ProcessComposeSSEClient{
		baseURL: baseURL,
	}
}

// OnProcessStatusChanged sets the handler for process status change events
func (c *ProcessComposeSSEClient) OnProcessStatusChanged(handler func(ProcessEvent)) {
	c.onProcessStatusChanged = handler
}

// OnLogLine sets the handler for log line events
func (c *ProcessComposeSSEClient) OnLogLine(handler func(LogEvent)) {
	c.onLogLine = handler
}

// OnSystemEvent sets the handler for system events
func (c *ProcessComposeSSEClient) OnSystemEvent(handler func(SystemEvent)) {
	c.onSystemEvent = handler
}

// OnError sets the error handler
func (c *ProcessComposeSSEClient) OnError(handler func(error)) {
	c.onError = handler
}

// OnDisconnect sets the disconnect handler
func (c *ProcessComposeSSEClient) OnDisconnect(handler func()) {
	c.onDisconnect = handler
}

// Connect establishes SSE connection to process-compose
func (c *ProcessComposeSSEClient) Connect(ctx context.Context) error {
	url := fmt.Sprintf("%s/events/all", c.baseURL)
	
	// Create SSE client with custom headers
	c.client = sse.NewClient(url)
	c.client.Headers = map[string]string{
		"Accept":        "text/event-stream",
		"Cache-Control": "no-cache",
	}
	
	// Set up reconnection
	c.client.ReconnectStrategy = &sse.BackoffStrategy{
		MinRetryDelay: 1 * time.Second,
		MaxRetryDelay: 30 * time.Second,
		MaxRetries:    -1, // Infinite retries
	}
	
	// Handle disconnections
	c.client.OnDisconnect(func(client *sse.Client) {
		log.Println("SSE connection lost, attempting reconnect...")
		if c.onDisconnect != nil {
			c.onDisconnect()
		}
	})
	
	// Subscribe to process status changes
	c.client.Subscribe("process_status_changed", func(msg *sse.Event) {
		if c.onProcessStatusChanged != nil {
			var event ProcessEvent
			if err := json.Unmarshal(msg.Data, &event); err != nil {
				if c.onError != nil {
					c.onError(fmt.Errorf("failed to parse process event: %w", err))
				}
				return
			}
			c.onProcessStatusChanged(event)
		}
	})
	
	// Subscribe to log events
	c.client.Subscribe("log_line", func(msg *sse.Event) {
		if c.onLogLine != nil {
			var event LogEvent
			if err := json.Unmarshal(msg.Data, &event); err != nil {
				if c.onError != nil {
					c.onError(fmt.Errorf("failed to parse log event: %w", err))
				}
				return
			}
			c.onLogLine(event)
		}
	})
	
	// Subscribe to system events
	c.client.Subscribe("system_event", func(msg *sse.Event) {
		if c.onSystemEvent != nil {
			var event SystemEvent
			if err := json.Unmarshal(msg.Data, &event); err != nil {
				if c.onError != nil {
					c.onError(fmt.Errorf("failed to parse system event: %w", err))
				}
				return
			}
			c.onSystemEvent(event)
		}
	})
	
	// Start listening in a goroutine
	go func() {
		if err := c.client.SubscribeRaw(func(msg *sse.Event) {
			// Handle any unmatched events
			log.Printf("Received unhandled event: %s", msg.Event)
		}); err != nil {
			if c.onError != nil {
				c.onError(fmt.Errorf("SSE connection failed: %w", err))
			}
		}
	}()
	
	return nil
}

// Disconnect closes the SSE connection
func (c *ProcessComposeSSEClient) Disconnect() {
	if c.client != nil {
		c.client.Unsubscribe(make(chan struct{}))
	}
}

// IsConnected checks if the SSE connection is active
func (c *ProcessComposeSSEClient) IsConnected() bool {
	if c.client == nil {
		return false
	}
	
	// Simple health check
	resp, err := http.Get(fmt.Sprintf("%s/live", c.baseURL))
	if err != nil {
		return false
	}
	defer resp.Body.Close()
	
	return resp.StatusCode == http.StatusOK
}

// Example usage
func main() {
	client := NewProcessComposeSSEClient("http://localhost:8080")
	
	// Set up event handlers
	client.OnProcessStatusChanged(func(event ProcessEvent) {
		fmt.Printf("🔄 Process %s changed to %s", event.Name, event.Status)
		if event.PID > 0 {
			fmt.Printf(" (PID: %d)", event.PID)
		}
		if event.ExitCode != nil {
			fmt.Printf(" (Exit: %d)", *event.ExitCode)
		}
		fmt.Println()
	})
	
	client.OnLogLine(func(event LogEvent) {
		fmt.Printf("📝 [%s] %s\n", event.Process, event.Line)
	})
	
	client.OnSystemEvent(func(event SystemEvent) {
		fmt.Printf("🔧 System: %s - %s\n", event.Type, event.Message)
	})
	
	client.OnError(func(err error) {
		log.Printf("❌ SSE Error: %v", err)
	})
	
	client.OnDisconnect(func() {
		fmt.Println("🔌 SSE connection lost")
	})
	
	// Connect and listen
	ctx := context.Background()
	if err := client.Connect(ctx); err != nil {
		log.Fatal("Failed to connect:", err)
	}
	
	fmt.Println("🚀 Connected to process-compose SSE stream")
	fmt.Println("Press Ctrl+C to exit")
	
	// Keep the program running
	select {}
}
