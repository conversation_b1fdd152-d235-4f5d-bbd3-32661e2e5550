package main

import (
	"context"
	"fmt"
	"log"
	"net/http"
	"os"
	"os/signal"
	"sync"
	"syscall"
	"time"

	"github.com/danielgtaylor/huma/v2"
	"github.com/danielgtaylor/huma/v2/adapters/humachi"
	"github.com/danielgtaylor/huma/v2/sse"
	"github.com/go-chi/chi/v5"
	"github.com/go-chi/chi/v5/middleware"
)

// ProcessState represents the state of a process (matching process-compose types)
type ProcessState struct {
	Name             string  `json:"name" doc:"Process name"`
	Namespace        string  `json:"namespace" doc:"Process namespace"`
	Status           string  `json:"status" doc:"Current status (Running, Completed, Failed, etc.)"`
	SystemTime       string  `json:"system_time" doc:"System time information"`
	Age              int64   `json:"age" doc:"Age in nanoseconds"`
	IsReady          string  `json:"is_ready" doc:"Readiness status"`
	HasReadyProbe    bool    `json:"has_ready_probe" doc:"Whether process has readiness probe"`
	Restarts         int     `json:"restarts" doc:"Number of restarts"`
	ExitCode         int     `json:"exit_code" doc:"Exit code of the process"`
	PID              int     `json:"pid" doc:"Process ID"`
	IsElevated       bool    `json:"is_elevated" doc:"Whether process runs with elevated privileges"`
	PasswordProvided bool    `json:"password_provided" doc:"Whether password was provided"`
	Mem              int64   `json:"mem" doc:"Memory usage in bytes"`
	CPU              float64 `json:"cpu" doc:"CPU usage percentage"`
	IsRunning        bool    `json:"is_running" doc:"Whether process is currently running"`
}

// ProcessesResponse represents the response for getting all processes
type ProcessesResponse struct {
	Data []ProcessState `json:"data" doc:"List of all processes"`
}

// ProjectState represents the overall project state
type ProjectState struct {
	HostName          string `json:"hostName" doc:"Host name"`
	UserName          string `json:"userName" doc:"User name"`
	Version           string `json:"version" doc:"Process-compose version"`
	StartTime         string `json:"startTime" doc:"Project start time"`
	UpTime            int64  `json:"upTime" doc:"Uptime in seconds"`
	ProcessNum        int    `json:"processNum" doc:"Total number of processes"`
	RunningProcessNum int    `json:"runningProcessNum" doc:"Number of running processes"`
}

// SSE Event types for real-time updates
type ProcessStatusEvent struct {
	Type      string       `json:"type" doc:"Event type"`
	Process   ProcessState `json:"process" doc:"Process that changed"`
	Timestamp time.Time    `json:"timestamp" doc:"When the event occurred"`
}

type LogEvent struct {
	ProcessName string    `json:"process_name" doc:"Name of the process"`
	Line        string    `json:"line" doc:"Log line content"`
	Timestamp   time.Time `json:"timestamp" doc:"When the log was generated"`
}

type SystemEvent struct {
	Type      string    `json:"type" doc:"System event type"`
	Message   string    `json:"message" doc:"Event message"`
	Timestamp time.Time `json:"timestamp" doc:"When the event occurred"`
}

// Mock process manager (in real implementation, this would use process-compose internals)
type ProcessManager struct {
	processes map[string]*ProcessState
	mutex     sync.RWMutex

	// Event channels for SSE
	processEvents chan ProcessStatusEvent
	logEvents     chan LogEvent
	systemEvents  chan SystemEvent
}

func NewProcessManager() *ProcessManager {
	pm := &ProcessManager{
		processes:     make(map[string]*ProcessState),
		processEvents: make(chan ProcessStatusEvent, 100),
		logEvents:     make(chan LogEvent, 100),
		systemEvents:  make(chan SystemEvent, 100),
	}

	// Initialize with some example processes (matching your process-compose.yaml)
	pm.processes["example1"] = &ProcessState{
		Name:      "example1",
		Namespace: "default",
		Status:    "Completed",
		PID:       0,
		ExitCode:  0,
		IsRunning: false,
	}

	pm.processes["example2"] = &ProcessState{
		Name:      "example2",
		Namespace: "default",
		Status:    "Running",
		PID:       12345,
		IsRunning: true,
		Mem:       1024 * 1024, // 1MB
		CPU:       0.5,
	}

	pm.processes["example3"] = &ProcessState{
		Name:      "example3",
		Namespace: "default",
		Status:    "Completed",
		PID:       0,
		ExitCode:  0,
		IsRunning: false,
	}

	return pm
}

func (pm *ProcessManager) GetProcesses() ProcessesResponse {
	pm.mutex.RLock()
	defer pm.mutex.RUnlock()

	var processes []ProcessState
	for _, p := range pm.processes {
		processes = append(processes, *p)
	}

	return ProcessesResponse{Data: processes}
}

func (pm *ProcessManager) GetProcess(name string) (*ProcessState, error) {
	pm.mutex.RLock()
	defer pm.mutex.RUnlock()

	if p, exists := pm.processes[name]; exists {
		return p, nil
	}
	return nil, fmt.Errorf("process %s not found", name)
}

func (pm *ProcessManager) GetProjectState() ProjectState {
	pm.mutex.RLock()
	defer pm.mutex.RUnlock()

	running := 0
	for _, p := range pm.processes {
		if p.IsRunning {
			running++
		}
	}

	return ProjectState{
		HostName:          "localhost",
		UserName:          "user",
		Version:           "1.0.0-huma",
		StartTime:         time.Now().Format(time.RFC3339),
		ProcessNum:        len(pm.processes),
		RunningProcessNum: running,
	}
}

// Simulate process events for demonstration
func (pm *ProcessManager) StartEventSimulator() {
	go func() {
		ticker := time.NewTicker(5 * time.Second)
		defer ticker.Stop()

		counter := 0
		for range ticker.C {
			counter++

			// Simulate a process status change
			pm.mutex.Lock()
			if p, exists := pm.processes["example2"]; exists {
				if p.IsRunning {
					p.Status = "Restarting"
					p.Restarts++
				} else {
					p.Status = "Running"
					p.IsRunning = true
					p.PID = 12345 + counter
				}

				// Send SSE event
				select {
				case pm.processEvents <- ProcessStatusEvent{
					Type:      "process_status_changed",
					Process:   *p,
					Timestamp: time.Now(),
				}:
				default:
				}
			}
			pm.mutex.Unlock()

			// Simulate log events
			select {
			case pm.logEvents <- LogEvent{
				ProcessName: "example2",
				Line:        fmt.Sprintf("Log message #%d from example2", counter),
				Timestamp:   time.Now(),
			}:
			default:
			}

			// Simulate system events occasionally
			if counter%3 == 0 {
				select {
				case pm.systemEvents <- SystemEvent{
					Type:      "health_check",
					Message:   "System health check completed",
					Timestamp: time.Now(),
				}:
				default:
				}
			}
		}
	}()
}

// API Input/Output types
type GetProcessInput struct {
	Name string `path:"name" doc:"Process name"`
}

type GetProcessOutput struct {
	Body ProcessState `json:"process"`
}

type GetProcessesOutput struct {
	Body ProcessesResponse `json:"processes"`
}

type GetProjectOutput struct {
	Body ProjectState `json:"project"`
}

func main() {
	// Initialize process manager
	pm := NewProcessManager()
	pm.StartEventSimulator()

	// Create Chi router
	router := chi.NewRouter()
	router.Use(middleware.Logger)
	router.Use(middleware.Recoverer)
	router.Use(middleware.Heartbeat("/ping"))

	// Create Huma API
	config := huma.DefaultConfig("Process Compose API", "1.0.0")
	config.Info.Description = "Process Compose API with real-time SSE support powered by Huma v2"
	config.Servers = []*huma.Server{
		{URL: "http://localhost:8888", Description: "Development server"},
	}

	api := humachi.New(router, config)

	// Register REST API endpoints
	huma.Register(api, huma.Operation{
		OperationID: "getProcesses",
		Method:      http.MethodGet,
		Path:        "/processes",
		Summary:     "Get all processes",
		Description: "Retrieve the current state of all managed processes",
		Tags:        []string{"Processes"},
	}, func(ctx context.Context, input *struct{}) (*GetProcessesOutput, error) {
		processes := pm.GetProcesses()
		return &GetProcessesOutput{Body: processes}, nil
	})

	huma.Register(api, huma.Operation{
		OperationID: "getProcess",
		Method:      http.MethodGet,
		Path:        "/process/{name}",
		Summary:     "Get specific process",
		Description: "Retrieve the current state of a specific process by name",
		Tags:        []string{"Processes"},
	}, func(ctx context.Context, input *GetProcessInput) (*GetProcessOutput, error) {
		process, err := pm.GetProcess(input.Name)
		if err != nil {
			return nil, huma.Error404NotFound("Process not found", err)
		}
		return &GetProcessOutput{Body: *process}, nil
	})

	huma.Register(api, huma.Operation{
		OperationID: "getProject",
		Method:      http.MethodGet,
		Path:        "/project",
		Summary:     "Get project state",
		Description: "Retrieve the overall project state and statistics",
		Tags:        []string{"Project"},
	}, func(ctx context.Context, input *struct{}) (*GetProjectOutput, error) {
		project := pm.GetProjectState()
		return &GetProjectOutput{Body: project}, nil
	})

	// Register SSE endpoints for real-time updates
	sse.Register(api, huma.Operation{
		OperationID: "subscribeToProcessEvents",
		Method:      http.MethodGet,
		Path:        "/events/processes",
		Summary:     "Subscribe to process events",
		Description: "Real-time stream of process status changes via Server-Sent Events",
		Tags:        []string{"SSE", "Real-time"},
	}, map[string]any{
		"process_status_changed": ProcessStatusEvent{},
	}, func(ctx context.Context, input *struct{}, send sse.Sender) {
		log.Println("New SSE client connected to process events")

		// Send initial state
		processes := pm.GetProcesses()
		for _, process := range processes.Data {
			send.Data(ProcessStatusEvent{
				Type:      "initial_state",
				Process:   process,
				Timestamp: time.Now(),
			})
		}

		// Listen for events
		for {
			select {
			case <-ctx.Done():
				log.Println("SSE client disconnected from process events")
				return
			case event := <-pm.processEvents:
				send.Data(event)
			}
		}
	})

	sse.Register(api, huma.Operation{
		OperationID: "subscribeToLogEvents",
		Method:      http.MethodGet,
		Path:        "/events/logs",
		Summary:     "Subscribe to log events",
		Description: "Real-time stream of process log output via Server-Sent Events",
		Tags:        []string{"SSE", "Real-time", "Logs"},
	}, map[string]any{
		"log_line": LogEvent{},
	}, func(ctx context.Context, input *struct{}, send sse.Sender) {
		log.Println("New SSE client connected to log events")

		for {
			select {
			case <-ctx.Done():
				log.Println("SSE client disconnected from log events")
				return
			case event := <-pm.logEvents:
				send.Data(event)
			}
		}
	})

	sse.Register(api, huma.Operation{
		OperationID: "subscribeToAllEvents",
		Method:      http.MethodGet,
		Path:        "/events/all",
		Summary:     "Subscribe to all events",
		Description: "Real-time stream of all system events via Server-Sent Events",
		Tags:        []string{"SSE", "Real-time"},
	}, map[string]any{
		"process_status_changed": ProcessStatusEvent{},
		"log_line":               LogEvent{},
		"system_event":           SystemEvent{},
	}, func(ctx context.Context, input *struct{}, send sse.Sender) {
		log.Println("New SSE client connected to all events")

		// Send initial process states
		processes := pm.GetProcesses()
		for _, process := range processes.Data {
			send.Data(ProcessStatusEvent{
				Type:      "initial_state",
				Process:   process,
				Timestamp: time.Now(),
			})
		}

		for {
			select {
			case <-ctx.Done():
				log.Println("SSE client disconnected from all events")
				return
			case event := <-pm.processEvents:
				send.Data(event)
			case event := <-pm.logEvents:
				send.Data(event)
			case event := <-pm.systemEvents:
				send.Data(event)
			}
		}
	})

	// Health check endpoint
	huma.Register(api, huma.Operation{
		OperationID: "healthCheck",
		Method:      http.MethodGet,
		Path:        "/health",
		Summary:     "Health check",
		Description: "Check if the API is healthy and responsive",
		Tags:        []string{"Health"},
	}, func(ctx context.Context, input *struct{}) (*struct {
		Body struct {
			Status    string    `json:"status"`
			Timestamp time.Time `json:"timestamp"`
		} `json:"health"`
	}, error) {
		return &struct {
			Body struct {
				Status    string    `json:"status"`
				Timestamp time.Time `json:"timestamp"`
			} `json:"health"`
		}{
			Body: struct {
				Status    string    `json:"status"`
				Timestamp time.Time `json:"timestamp"`
			}{
				Status:    "healthy",
				Timestamp: time.Now(),
			},
		}, nil
	})

	// Start HTTP server
	server := &http.Server{
		Addr:    ":8888",
		Handler: router,
	}

	// Graceful shutdown
	go func() {
		sigChan := make(chan os.Signal, 1)
		signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)
		<-sigChan

		log.Println("Shutting down server...")
		ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
		defer cancel()

		if err := server.Shutdown(ctx); err != nil {
			log.Printf("Server shutdown error: %v", err)
		}
	}()

	// Start server
	log.Printf("🚀 Process Compose API with Huma v2 starting on http://localhost:8888")
	log.Printf("📚 API Documentation: http://localhost:8888/docs")
	log.Printf("🔄 SSE Process Events: http://localhost:8888/events/processes")
	log.Printf("📝 SSE Log Events: http://localhost:8888/events/logs")
	log.Printf("🌐 SSE All Events: http://localhost:8888/events/all")
	log.Printf("💚 Health Check: http://localhost:8888/health")

	if err := server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
		log.Fatalf("Server failed to start: %v", err)
	}

	log.Println("Server stopped")
}
