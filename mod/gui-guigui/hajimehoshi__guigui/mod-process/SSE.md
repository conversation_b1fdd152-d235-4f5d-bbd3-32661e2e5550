# Feature Request: Server-Sent Events (SSE) Support for Real-time Updates

## Summary

Add Server-Sent Events (SSE) endpoints to the process-compose REST API to enable real-time streaming of process status updates, logs, and system events without requiring constant polling.

## Motivation

Currently, process-compose provides excellent REST API endpoints for retrieving process information, but real-time monitoring requires constant polling of these endpoints. This approach has several limitations:

- **Inefficient**: Constant polling wastes bandwidth and server resources
- **Delayed Updates**: Polling intervals create delays in receiving updates
- **Complex Client Logic**: Clients must implement polling logic and handle rate limiting
- **Poor User Experience**: Web dashboards and monitoring tools feel sluggish

SSE would enable efficient real-time communication for building modern web interfaces and monitoring dashboards.

## Proposed Solution

Add the following SSE endpoints to complement the existing REST API:

### 1. Process Status Stream
```
GET /events/processes
Accept: text/event-stream
```

Stream real-time updates when process status changes (started, stopped, completed, failed, restarted).

**Example Event:**
```
event: process_status_changed
data: {"name": "example1", "status": "Running", "pid": 12345, "timestamp": "2024-06-12T18:15:30Z"}

event: process_status_changed  
data: {"name": "example2", "status": "Completed", "exit_code": 0, "timestamp": "2024-06-12T18:16:45Z"}
```

### 2. Process Logs Stream
```
GET /events/logs/{process_name}
Accept: text/event-stream
```

Stream real-time log output from a specific process.

**Example Event:**
```
event: log_line
data: {"process": "example1", "line": "Application started successfully", "timestamp": "2024-06-12T18:15:30Z"}
```

### 3. System Events Stream
```
GET /events/system
Accept: text/event-stream
```

Stream system-level events like project start/stop, configuration reloads, etc.

**Example Event:**
```
event: project_started
data: {"processes_count": 3, "timestamp": "2024-06-12T18:15:00Z"}

event: configuration_reloaded
data: {"config_files": ["process-compose.yaml"], "timestamp": "2024-06-12T18:20:00Z"}
```

### 4. Combined Events Stream
```
GET /events/all
Accept: text/event-stream
```

Stream all events in a single connection for comprehensive monitoring.

## Implementation Details

### Event Types
- `process_status_changed` - Process state transitions
- `process_started` - Process startup
- `process_stopped` - Process shutdown  
- `process_restarted` - Process restart
- `log_line` - New log output
- `project_started` - Project initialization
- `project_stopped` - Project shutdown
- `configuration_reloaded` - Config file changes
- `health_check_failed` - Health check failures
- `health_check_recovered` - Health check recovery

### Query Parameters
- `?filter=process1,process2` - Filter events for specific processes
- `?since=timestamp` - Start streaming from a specific time
- `?types=process_status_changed,log_line` - Filter by event types

### Connection Management
- Automatic reconnection support with `Last-Event-ID` header
- Configurable keep-alive intervals
- Graceful connection cleanup on client disconnect

### Configuration Options
Add new configuration options to control SSE behavior:

```yaml
options:
  sse:
    enabled: true
    max_connections: 100
    keep_alive_interval: 30s
    buffer_size: 1000
    log_streaming: true
```

## Use Cases

### 1. Web Dashboard Development
```javascript
const eventSource = new EventSource('/events/processes');
eventSource.onmessage = function(event) {
    const data = JSON.parse(event.data);
    updateProcessStatus(data.name, data.status);
};
```

### 2. Real-time Monitoring Tools
```bash
curl -H "Accept: text/event-stream" http://localhost:8080/events/all
```

### 3. CI/CD Integration
Real-time build status updates without polling delays.

### 4. Development Workflow
Live log tailing during development:
```bash
curl -H "Accept: text/event-stream" http://localhost:8080/events/logs/my-service
```

## Benefits

- **Reduced Server Load**: Eliminates constant polling
- **Real-time Updates**: Instant notification of changes
- **Better User Experience**: Responsive web interfaces
- **Standard Protocol**: SSE is widely supported by browsers and tools
- **Backward Compatible**: Existing REST API remains unchanged
- **Efficient**: Only sends data when changes occur

## Alternative Approaches Considered

1. **WebSockets**: More complex, bidirectional when only server-to-client is needed
2. **Long Polling**: Still requires client-side complexity and connection management
3. **gRPC Streaming**: Would require additional dependencies and complexity

SSE is the most appropriate choice for this use case as it's:
- Simple to implement and use
- Natively supported by browsers
- Automatically handles reconnection
- Perfect for server-to-client streaming

## Implementation Priority

**High Priority:**
- Basic process status change events
- Process log streaming

**Medium Priority:**  
- System events
- Event filtering and querying
- Configuration options

**Low Priority:**
- Advanced filtering
- Event replay functionality
- Metrics and analytics events

## Compatibility

This feature would be:
- ✅ **Backward Compatible**: No changes to existing REST API
- ✅ **Optional**: Can be disabled via configuration
- ✅ **Standards Compliant**: Uses standard SSE protocol
- ✅ **Cross-Platform**: Works on all platforms process-compose supports

## Related Issues

This feature would complement the existing REST API and TUI functionality, providing a modern foundation for building web-based monitoring and control interfaces for process-compose.
