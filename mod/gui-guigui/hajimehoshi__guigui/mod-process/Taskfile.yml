# https://taskfile.dev

version: '3'

vars:
  GREETING: Hello, <PERSON>!

tasks:
  default:
    cmds:
      - echo "{{.GREETING}}"
    silent: true

  build:
    cmds:
      - cd ./process-compose && go build -o ./.bin/process-compose
      - cd ./process-compose-huma && go build -o ./.bin/process-compose-huma
      - cd ./process-compose-datastar && go build -o ./.bin/process-compose-datastar
      
  pack:up:
    cmds:
      # copy bins and config up
      - mkdir -p ./.bin
      - cp -r ./process-compose/.bin/. ./.bin/
      - cp -r ./process-compose-huma/.bin/. ./.bin/
      - cp -r ./process-compose-datastar/.bin/. ./.bin/
      # copy config into .bin
      - cp -r ./process-compose.yaml ./.bin/

  pack:del:
      - rm -rf ./.bin/
  pack:list:
      - ls -al ./.bin/

  run:
    cmds:
      - cd ./.bin && ./process-compose-huma

